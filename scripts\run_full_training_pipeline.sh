#!/bin/bash

# Full Training Pipeline Automation Script
# This script automates the complete training pipeline:
# 1. UNet training -> 2. UNet inference -> 3. UNet evaluation
# 4. SAM2 training -> 5. SAM2 inference -> 6. SAM2 evaluation
# Supports multiple rounds of training

# Usage: ./run_full_training_pipeline.sh [CUDA_DEVICES] [NUM_ROUNDS] [START_ROUND] [START_STEP]
# Example: ./run_full_training_pipeline.sh 0,1 3
# Example: ./run_full_training_pipeline.sh 0,1 3 2 4  # Start from round 2, step 4 (SAM2 training)
#
# Steps:
#   1: UNet Training
#   2: UNet Inference
#   3: UNet Evaluation
#   4: SAM2 Training
#   5: SAM2 Inference
#   6: SAM2 Evaluation

set -e  # Exit on any error

# Default parameters
DEFAULT_CUDA_DEVICES="0"
DEFAULT_NUM_ROUNDS=1
DEFAULT_START_ROUND=1
DEFAULT_START_STEP=1

# Parse command line arguments
CUDA_DEVICES="${1:-$DEFAULT_CUDA_DEVICES}"
NUM_ROUNDS="${2:-$DEFAULT_NUM_ROUNDS}"
START_ROUND="${3:-$DEFAULT_START_ROUND}"
START_STEP="${4:-$DEFAULT_START_STEP}"

# Step definitions
STEP_UNET_TRAIN=1
STEP_UNET_INFERENCE=2
STEP_UNET_EVAL=3
STEP_SAM2_TRAIN=4
STEP_SAM2_INFERENCE=5
STEP_SAM2_EVAL=6

# Set CUDA devices
export CUDA_VISIBLE_DEVICES="$CUDA_DEVICES"
echo "Using CUDA devices: $CUDA_VISIBLE_DEVICES"
echo "Number of training rounds: $NUM_ROUNDS"
echo "Starting from round: $START_ROUND, step: $START_STEP"

# Validate start parameters
if [[ $START_ROUND -lt 1 || $START_ROUND -gt $NUM_ROUNDS ]]; then
    echo "❌ ERROR: START_ROUND ($START_ROUND) must be between 1 and $NUM_ROUNDS"
    exit 1
fi

if [[ $START_STEP -lt 1 || $START_STEP -gt 6 ]]; then
    echo "❌ ERROR: START_STEP ($START_STEP) must be between 1 and 6"
    echo "  1: UNet Training"
    echo "  2: UNet Inference"
    echo "  3: UNet Evaluation"
    echo "  4: SAM2 Training"
    echo "  5: SAM2 Inference"
    echo "  6: SAM2 Evaluation"
    exit 1
fi

# Get script directory and project root
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Change to project root
cd "$PROJECT_ROOT"
echo "Project root: $PROJECT_ROOT"

# Function to log with timestamp
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to run command with error handling
run_command() {
    local cmd="$1"
    local description="$2"

    log "Starting: $description"
    log "Command: $cmd"

    if eval "$cmd"; then
        log "✅ SUCCESS: $description"
        return 0
    else
        log "❌ FAILED: $description"
        echo "Command failed: $cmd"
        echo "Do you want to continue? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            log "Stopping execution"
            exit 1
        fi
        return 1
    fi
}

# Function to restore state from previous rounds
restore_previous_state() {
    local target_round="$1"

    log "🔄 Restoring state for round $target_round..."

    # Restore UNet path from previous round
    if [[ $target_round -gt 1 ]]; then
        local prev_round=$((target_round - 1))
        PREV_UNET_PATH=$(get_latest_unet_checkpoint "$prev_round")
        if [[ -z "$PREV_UNET_PATH" ]]; then
            log "❌ ERROR: Cannot find UNet checkpoint for previous round $prev_round"
            exit 1
        fi
        log "📁 Restored previous UNet path: $PREV_UNET_PATH"

        # Restore SAM2 experiment from previous round
        PREV_SAM2_EXP=$(get_sam2_experiment_for_round "$prev_round")
        if [[ -z "$PREV_SAM2_EXP" ]]; then
            log "❌ ERROR: Cannot find SAM2 experiment for previous round $prev_round"
            exit 1
        fi
        log "📁 Restored previous SAM2 experiment: $PREV_SAM2_EXP"
    fi
}

# Function to check if step should be skipped
should_skip_step() {
    local current_round="$1"
    local current_step="$2"

    if [[ $current_round -lt $START_ROUND ]]; then
        return 0  # Skip (true)
    elif [[ $current_round -eq $START_ROUND && $current_step -lt $START_STEP ]]; then
        return 0  # Skip (true)
    else
        return 1  # Don't skip (false)
    fi
}

# Function to get the latest checkpoint path
get_latest_unet_checkpoint() {
    local round_num="$1"
    local checkpoint_dir="${PROJECT_ROOT}/output/fs_unet_ckpt/mitoem_${round_num}"
    
    if [[ -f "$checkpoint_dir/last_checkpoint.pytorch" ]]; then
        echo "$checkpoint_dir/last_checkpoint.pytorch"
    elif [[ -f "$checkpoint_dir/best_checkpoint.pytorch" ]]; then
        echo "$checkpoint_dir/best_checkpoint.pytorch"
    else
        # Find the latest checkpoint file
        local latest_checkpoint=$(find "$checkpoint_dir" -name "*.pytorch" -type f 2>/dev/null | sort | tail -1)
        if [[ -n "$latest_checkpoint" ]]; then
            echo "$latest_checkpoint"
        else
            echo ""
        fi
    fi
}

# Function to get the SAM2 experiment directory for a specific round
get_sam2_experiment_for_round() {
    local round_num="$1"
    local sam2_exp_dir="${PROJECT_ROOT}/output/finetune_sam_ckpt/mitoem_${round_num}"

    if [[ -d "$sam2_exp_dir" && -f "$sam2_exp_dir/checkpoints/checkpoint.pt" ]]; then
        echo "$sam2_exp_dir"
    else
        echo ""
    fi
}

# Function to get the latest SAM2 experiment directory (fallback for legacy format)
get_latest_sam2_experiment() {
    local sam2_ckpt_dir="${PROJECT_ROOT}/output/finetune_sam_ckpt"

    if [[ -d "$sam2_ckpt_dir" ]]; then
        # First try to find mitoem_* format directories
        local latest_exp=$(find "$sam2_ckpt_dir" -maxdepth 1 -type d -name "mitoem_*" 2>/dev/null | sort -V | tail -1)
        if [[ -n "$latest_exp" && -f "$latest_exp/checkpoints/checkpoint.pt" ]]; then
            echo "$latest_exp"
            return
        fi

        # Fallback to timestamp format directories
        latest_exp=$(find "$sam2_ckpt_dir" -maxdepth 1 -type d -name "20*" 2>/dev/null | sort | tail -1)
        if [[ -n "$latest_exp" && -f "$latest_exp/checkpoints/checkpoint.pt" ]]; then
            echo "$latest_exp"
        else
            echo ""
        fi
    else
        echo ""
    fi
}

# Main training pipeline
log "🚀 Starting full training pipeline"
log "Configuration:"
log "  - CUDA devices: $CUDA_DEVICES"
log "  - Number of rounds: $NUM_ROUNDS"
log "  - Starting from round: $START_ROUND, step: $START_STEP"
log "  - Project root: $PROJECT_ROOT"

# Initialize variables for tracking paths
PREV_UNET_PATH=""
PREV_SAM2_EXP=""

# Restore state if starting from a later round or step
if [[ $START_ROUND -gt 1 || $START_STEP -gt 1 ]]; then
    log "🔄 Restoring state from previous execution..."

    # Restore state for all completed rounds
    for restore_round in $(seq 1 $((START_ROUND - 1))); do
        restore_previous_state $restore_round
    done

    # If starting from middle of a round, restore that round's state too
    if [[ $START_STEP -gt 1 ]]; then
        restore_previous_state $START_ROUND
    fi
fi

for round in $(seq 1 $NUM_ROUNDS); do
    log ""
    log "🔄 ===== ROUND $round/$NUM_ROUNDS ====="
    
    # Step 1: UNet Training
    log ""
    log "📚 Step 1: UNet Training (Round $round)"

    if should_skip_step $round $STEP_UNET_TRAIN; then
        log "⏭️  Skipping UNet Training (Round $round)"
    else
        # Use absolute path for checkpoint directory
        checkpoint_dir="$PROJECT_ROOT/output/fs_unet_ckpt/mitoem_${round}"

        if [[ $round -eq 1 ]]; then
            # First round: use trainu_fs_mitoem config
            unet_config="trainu_fs_mitoem"
            unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"$checkpoint_dir\""
        else
            # Subsequent rounds: use trainu_fs_mitoem2 config with previous checkpoint
            unet_config="trainu_fs_mitoem2"
            if [[ -z "$PREV_UNET_PATH" ]]; then
                log "❌ ERROR: Previous UNet checkpoint not found for round $round"
                exit 1
            fi
            unet_cmd="python train/train_unet.py +task=$unet_config ++task.override_unet_config.trainer.checkpoint_dir=\"$checkpoint_dir\" ++task.override_unet_config.trainer.pre_trained=\"$PREV_UNET_PATH\""
        fi

        run_command "$unet_cmd" "UNet Training Round $round"
    fi
    
    # Get current round UNet checkpoint path
    CURRENT_UNET_PATH=$(get_latest_unet_checkpoint "$round")
    if [[ -z "$CURRENT_UNET_PATH" ]]; then
        log "❌ ERROR: UNet checkpoint not found after training round $round"
        exit 1
    fi
    log "📁 UNet checkpoint for round $round: $CURRENT_UNET_PATH"

    # Step 2: UNet Inference
    log ""
    log "🔍 Step 2: UNet Inference (Round $round)"

    if should_skip_step $round $STEP_UNET_INFERENCE; then
        log "⏭️  Skipping UNet Inference (Round $round)"
    else
        unet_inference_cmd="python predict/predict_new.py +task=in_mitoem_u ++task.override_unet_config.model_path=\"$CURRENT_UNET_PATH\""
        run_command "$unet_inference_cmd" "UNet Inference Round $round"
    fi

    # Step 3: UNet Evaluation
    log ""
    log "📊 Step 3: UNet Evaluation (Round $round)"

    if should_skip_step $round $STEP_UNET_EVAL; then
        log "⏭️  Skipping UNet Evaluation (Round $round)"
    else
        unet_eval_cmd="python predict/evaluate.py --config-name evaluate_mitoem_u ++custom_suffix=\"${round}_unet\""
        run_command "$unet_eval_cmd" "UNet Evaluation Round $round"
    fi
    
    # Step 4: SAM2 Training
    log ""
    log "🎯 Step 4: SAM2 Training (Round $round)"

    if should_skip_step $round $STEP_SAM2_TRAIN; then
        log "⏭️  Skipping SAM2 Training (Round $round)"
    else
        # Use absolute path for SAM2 experiment directory
        sam2_exp_dir="$PROJECT_ROOT/output/finetune_sam_ckpt/mitoem_${round}"

        if [[ $round -eq 1 ]]; then
            # First round: use default config
            sam2_cmd="python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.experiment_log_dir=\"$sam2_exp_dir\""
        else
            # Subsequent rounds: use previous SAM2 checkpoint
            if [[ -z "$PREV_SAM2_EXP" ]]; then
                log "❌ ERROR: Previous SAM2 experiment not found for round $round"
                exit 1
            fi
            sam2_checkpoint_path="$PREV_SAM2_EXP/checkpoints/checkpoint.pt"
            sam2_cmd="python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.ckpt_path=\"$sam2_checkpoint_path\" ++task.config_overrides.launcher.experiment_log_dir=\"$sam2_exp_dir\""
        fi

        run_command "$sam2_cmd" "SAM2 Training Round $round"
    fi
    
    # Get current round SAM2 experiment path
    CURRENT_SAM2_EXP=$(get_sam2_experiment_for_round "$round")
    if [[ -z "$CURRENT_SAM2_EXP" ]]; then
        log "❌ ERROR: SAM2 experiment not found after training round $round"
        exit 1
    fi
    log "📁 SAM2 experiment for round $round: $CURRENT_SAM2_EXP"
    
    # Step 5: SAM2 Inference
    log ""
    log "🔍 Step 5: SAM2 Inference (Round $round)"

    if should_skip_step $round $STEP_SAM2_INFERENCE; then
        log "⏭️  Skipping SAM2 Inference (Round $round)"
    else
        sam2_model_path="$CURRENT_SAM2_EXP/checkpoints/checkpoint.pt"
        sam2_inference_cmd="python predict/predict_new.py +task=in_mitoem ++task.sam2_model_path=\"$sam2_model_path\" ++task.override_unet_config.model_path=\"$CURRENT_UNET_PATH\""
        run_command "$sam2_inference_cmd" "SAM2 Inference Round $round"
    fi

    # Step 6: SAM2 Evaluation
    log ""
    log "📊 Step 6: SAM2 Evaluation (Round $round)"

    if should_skip_step $round $STEP_SAM2_EVAL; then
        log "⏭️  Skipping SAM2 Evaluation (Round $round)"
    else
        sam2_eval_cmd="python predict/evaluate.py --config-name evaluate_mitoem ++custom_suffix=\"${round}_sam\""
        run_command "$sam2_eval_cmd" "SAM2 Evaluation Round $round"
    fi
    
    # Update previous paths for next round
    PREV_UNET_PATH="$CURRENT_UNET_PATH"
    PREV_SAM2_EXP="$CURRENT_SAM2_EXP"
    
    log "✅ Round $round completed successfully"
done

log ""
log "🎉 All $NUM_ROUNDS rounds completed successfully!"
log "Finished at: $(date)"

# Summary
log ""
log "📋 Training Summary:"
log "  - Total rounds: $NUM_ROUNDS"
log "  - Final UNet checkpoint: $PREV_UNET_PATH"
log "  - Final SAM2 experiment: $PREV_SAM2_EXP"
log "  - Results can be found in the output directory"
