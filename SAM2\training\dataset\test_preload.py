"""
Simple test for VolumeDataset preloading functionality.
"""

import time
import numpy as np
import threading
import sys
import os

# Add the parent directory to sys.path to import VolumeDataset
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, parent_dir)


class MockVolumeIO:
    """Mock volume loader for demonstration purposes."""

    def load(self, path):
        # Simulate loading time with different delays for different files
        if "volume" in path:
            time.sleep(0.3)  # Volume files take longer
            print(f"Loading volume from {path}")
            return np.random.rand(32, 32, 32).astype(np.float32)
        else:
            time.sleep(0.2)  # Mask files are faster
            print(f"Loading masks from {path}")
            return np.random.randint(0, 2, (32, 32, 32)).astype(np.uint8)


class MockVOSVideo:
    """Mock VOSVideo class."""

    def __init__(self, video_id, idx, frames):
        self.video_id = video_id
        self.idx = idx
        self.frames = frames


class MockSegmentLoader:
    """Mock segment loader."""

    def __init__(self, masks, single_object_mode, mask_to_binary):
        self.masks = masks
        self.single_object_mode = single_object_mode
        self.mask_to_binary = mask_to_binary


class MockRefMaskLoader:
    """Mock ref mask loader."""

    def __init__(self, ref_masks, to_logits=False):
        self.ref_masks = ref_masks
        self.to_logits = to_logits


class TestVolumeDataset:
    """Simplified test class for VolumeDataset preloading."""

    def __init__(self, enable_preloading=True):
        self.enable_preloading = enable_preloading
        self.volume_loader = MockVolumeIO()
        self.resolution = 32
        self.mask_to_binary = True
        self.scale_mask_value = False
        self.single_object_mode = True
        self.use_ref_mask = False

        # Create mock datasets
        self.datasets = []
        for i in range(3):
            self.datasets.append(
                {
                    "volume_path": f"/mock/volume_{i}.npy",
                    "masks_path": f"/mock/masks_{i}.npy",
                    "ref_masks_path": None,
                    "few_masks_paths": None,
                    "volume_mean": 0.5,
                    "volume_std": 0.2,
                }
            )

        # Initialize state
        self.active_volume = None
        self.active_segment_loader = None
        self.active_ref_mask_loader = None
        self.active_few_masks_info = None
        self.current_idx = None

        # Preloading attributes
        self.preload_thread = None
        self.preload_lock = threading.Lock()
        self.preload_ready_event = threading.Event()
        self.preloaded_idx = None
        self.preloaded_data = None

        # Performance monitoring
        self.preload_stats = {
            "total_preloads": 0,
            "successful_preloads": 0,
            "failed_preloads": 0,
            "preload_hits": 0,
            "preload_misses": 0,
        }

    def _load_dataset_data(self, idx):
        """Load dataset data for a given index."""
        dataset = self.datasets[idx]
        volume_path = dataset["volume_path"]
        masks_path = dataset["masks_path"]
        volume_mean = dataset["volume_mean"]
        volume_std = dataset["volume_std"]

        volume = self.volume_loader.load(volume_path)
        masks = self.volume_loader.load(masks_path)

        # Mock frame creation
        frames = [f"frame_{i}" for i in range(5)]

        volume_obj = MockVOSVideo(idx, idx, frames)
        segment_loader = MockSegmentLoader(
            masks, self.single_object_mode, self.mask_to_binary
        )
        ref_mask_loader = None
        few_masks_info = None

        return volume_obj, segment_loader, ref_mask_loader, few_masks_info

    def _preload_worker(self, target_idx):
        """Worker function for preloading data in background thread."""
        try:
            print(f"Starting preload for dataset {target_idx}")
            self.preload_stats["total_preloads"] += 1

            data = self._load_dataset_data(target_idx)

            with self.preload_lock:
                self.preloaded_idx = target_idx
                self.preloaded_data = data
                self.preload_ready_event.set()

            self.preload_stats["successful_preloads"] += 1
            print(f"Preload completed for dataset {target_idx}")
        except Exception as e:
            print(f"Preload failed for dataset {target_idx}: {e}")
            self.preload_stats["failed_preloads"] += 1

            with self.preload_lock:
                self.preloaded_idx = None
                self.preloaded_data = None
                self.preload_ready_event.set()

    def _start_preload(self, next_idx):
        """Start preloading the next dataset in background."""
        if not self.enable_preloading:
            return

        # Don't preload if it's the same as current or already preloaded
        if next_idx == self.current_idx or next_idx == self.preloaded_idx:
            return

        # Cancel any existing preload
        self._cancel_preload()

        # Start new preload
        self.preload_ready_event.clear()
        self.preload_thread = threading.Thread(
            target=self._preload_worker,
            args=(next_idx,),
            daemon=True,
            name=f"TestDataset-Preload-{next_idx}",
        )
        self.preload_thread.start()

    def _cancel_preload(self):
        """Cancel any ongoing preload operation."""
        if self.preload_thread and self.preload_thread.is_alive():
            with self.preload_lock:
                self.preloaded_idx = None
                self.preloaded_data = None

    def get_video(self, idx):
        """Get video data with preloading support."""
        # Check if we need to load new data
        if self.active_volume is None or self.active_volume.video_id != idx:
            # Check if preloaded data is available
            if self.enable_preloading and self.preloaded_idx == idx:
                print(f"Using preloaded data for dataset {idx}")
                # Wait for preload to complete if still in progress
                self.preload_ready_event.wait()

                with self.preload_lock:
                    if self.preloaded_data is not None:
                        # Use preloaded data
                        (
                            self.active_volume,
                            self.active_segment_loader,
                            self.active_ref_mask_loader,
                            self.active_few_masks_info,
                        ) = self.preloaded_data

                        # Clear preloaded data
                        self.preloaded_idx = None
                        self.preloaded_data = None

                        # Update statistics
                        self.preload_stats["preload_hits"] += 1
                    else:
                        # Preload failed, load synchronously
                        print(f"Preload failed, loading dataset {idx} synchronously")
                        (
                            self.active_volume,
                            self.active_segment_loader,
                            self.active_ref_mask_loader,
                            self.active_few_masks_info,
                        ) = self._load_dataset_data(idx)

                        # Update statistics
                        self.preload_stats["preload_misses"] += 1
            else:
                # Load synchronously
                print(f"Loading dataset {idx} synchronously")
                (
                    self.active_volume,
                    self.active_segment_loader,
                    self.active_ref_mask_loader,
                    self.active_few_masks_info,
                ) = self._load_dataset_data(idx)

                # Update statistics
                self.preload_stats["preload_misses"] += 1

            # Start preloading next dataset
            if self.enable_preloading:
                next_idx = (idx + 1) % len(self.datasets)
                self._start_preload(next_idx)

            self.current_idx = idx

        return (
            self.active_volume,
            self.active_segment_loader,
            self.active_ref_mask_loader,
            self.active_few_masks_info,
        )

    def get_preload_stats(self):
        """Get preloading performance statistics."""
        stats = self.preload_stats.copy()

        # Calculate derived statistics
        total_requests = stats["preload_hits"] + stats["preload_misses"]
        if total_requests > 0:
            stats["hit_rate"] = stats["preload_hits"] / total_requests
        else:
            stats["hit_rate"] = 0.0

        if stats["total_preloads"] > 0:
            stats["success_rate"] = (
                stats["successful_preloads"] / stats["total_preloads"]
            )
        else:
            stats["success_rate"] = 0.0

        return stats

    def print_preload_stats(self):
        """Print preloading performance statistics."""
        stats = self.get_preload_stats()

        print("=== Preloading Statistics ===")
        print(f"Preloading enabled: {self.enable_preloading}")
        print(f"Total preload attempts: {stats['total_preloads']}")
        print(f"Successful preloads: {stats['successful_preloads']}")
        print(f"Failed preloads: {stats['failed_preloads']}")
        print(f"Preload success rate: {stats['success_rate']:.2%}")
        print(f"Preload hits: {stats['preload_hits']}")
        print(f"Preload misses: {stats['preload_misses']}")
        print(f"Preload hit rate: {stats['hit_rate']:.2%}")
        print("=" * 30)

    def cleanup(self):
        """Clean up resources."""
        self._cancel_preload()
        if self.preload_thread and self.preload_thread.is_alive():
            self.preload_thread.join(timeout=5.0)

        if self.enable_preloading:
            self.print_preload_stats()


def test_with_preloading():
    """Test with preloading enabled."""
    print("=== Testing WITH Preloading ===")
    dataset = TestVolumeDataset(enable_preloading=True)

    start_time = time.time()

    # Pre-start the first preload to simulate real training scenario
    print("Pre-loading first dataset...")
    dataset._start_preload(0)
    time.sleep(0.6)  # Give time for first preload to complete

    # Simulate training epochs with longer training time
    for epoch in range(4):  # Test more epochs
        print(f"\n--- Epoch {epoch} ---")
        curr_volume = epoch % len(dataset.datasets)

        print(f"Requesting dataset {curr_volume}")
        epoch_start = time.time()

        _ = dataset.get_video(curr_volume)

        epoch_time = time.time() - epoch_start
        print(f"Dataset {curr_volume} ready in {epoch_time:.3f} seconds")

        # Simulate longer training time to allow preloading to complete
        print(f"Training on dataset {curr_volume}...")
        time.sleep(0.8)  # Longer training simulation

    total_time = time.time() - start_time
    dataset.cleanup()

    return total_time


def test_without_preloading():
    """Test without preloading."""
    print("\n=== Testing WITHOUT Preloading ===")
    dataset = TestVolumeDataset(enable_preloading=False)

    start_time = time.time()

    # Simulate training epochs with same timing as preload test
    for epoch in range(4):  # Same number of epochs
        print(f"\n--- Epoch {epoch} ---")
        curr_volume = epoch % len(dataset.datasets)

        print(f"Requesting dataset {curr_volume}")
        epoch_start = time.time()

        _ = dataset.get_video(curr_volume)

        epoch_time = time.time() - epoch_start
        print(f"Dataset {curr_volume} ready in {epoch_time:.3f} seconds")

        # Simulate same training time
        print(f"Training on dataset {curr_volume}...")
        time.sleep(0.8)  # Same training simulation time

    total_time = time.time() - start_time
    dataset.cleanup()

    return total_time


if __name__ == "__main__":
    print("Testing VolumeDataset Preloading Functionality")
    print("=" * 50)

    # Test with preloading
    with_preload_time = test_with_preloading()

    # Test without preloading
    without_preload_time = test_without_preloading()

    # Compare results
    print(f"\n=== Performance Comparison ===")
    print(f"With preloading:    {with_preload_time:.3f} seconds")
    print(f"Without preloading: {without_preload_time:.3f} seconds")
    if with_preload_time > 0:
        speedup = without_preload_time / with_preload_time
        print(f"Speedup:           {speedup:.2f}x")
    print("=" * 35)
