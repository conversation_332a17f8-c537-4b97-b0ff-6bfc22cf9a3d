#!/usr/bin/env python3
"""
Full Training Pipeline Automation Script (Python Version)

This script automates the complete training pipeline:
1. UNet training -> 2. UNet inference -> 3. UNet evaluation
4. SAM2 training -> 5. SAM2 inference -> 6. SAM2 evaluation
Supports multiple rounds of training with checkpoint cleanup functionality.

Usage: python run_full_training_pipeline.py [options]
Example: python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3
Example: python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 --start-round 2 --start-step 4

Steps:
  1: UNet Training
  2: UNet Inference
  3: UNet Evaluation
  4: SAM2 Training
  5: SAM2 Inference
  6: SAM2 Evaluation
"""

import argparse
import os
import sys
import subprocess
import shutil
import glob
from datetime import datetime
from pathlib import Path
from typing import Optional, List


class TrainingPipeline:
    """Full training pipeline automation class"""

    # Step definitions
    STEP_UNET_TRAIN = 1
    STEP_UNET_INFERENCE = 2
    STEP_UNET_EVAL = 3
    STEP_SAM2_TRAIN = 4
    STEP_SAM2_INFERENCE = 5
    STEP_SAM2_EVAL = 6

    def __init__(
        self,
        cuda_devices: str = "0",
        num_rounds: int = 1,
        start_round: int = 1,
        start_step: int = 1,
        log_dir: Optional[str] = None,
        clean_checkpoints: bool = False,
    ):
        """
        Initialize training pipeline

        Args:
            cuda_devices: CUDA devices to use (e.g., "0,1")
            num_rounds: Number of training rounds
            start_round: Starting round number
            start_step: Starting step number
            log_dir: Directory to save logs (optional)
            clean_checkpoints: Whether to clean previous checkpoints
        """
        self.cuda_devices = cuda_devices
        self.num_rounds = num_rounds
        self.start_round = start_round
        self.start_step = start_step
        self.clean_checkpoints = clean_checkpoints

        # Get script directory and project root
        self.script_dir = Path(__file__).parent.absolute()
        self.project_root = self.script_dir.parent

        # Setup logging
        self.setup_logging(log_dir)

        # Initialize tracking variables
        self.prev_unet_path = ""
        self.prev_sam2_exp = ""

        # Validate parameters
        self._validate_parameters()

    def setup_logging(self, log_dir: Optional[str]):
        """Setup logging configuration"""
        if log_dir:
            self.log_dir = Path(log_dir)
            self.log_dir.mkdir(parents=True, exist_ok=True)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.log_file = self.log_dir / f"training_pipeline_{timestamp}.log"
        else:
            self.log_file = None

    def log(self, message: str):
        """Log message with timestamp"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        print(log_message)

        if self.log_file:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(log_message + "\n")

    def _validate_parameters(self):
        """Validate input parameters"""
        if not (1 <= self.start_round <= self.num_rounds):
            self.log(
                f"❌ ERROR: START_ROUND ({self.start_round}) must be between 1 and {self.num_rounds}"
            )
            sys.exit(1)

        if not (1 <= self.start_step <= 6):
            self.log("❌ ERROR: START_STEP must be between 1 and 6")
            self.log("  1: UNet Training")
            self.log("  2: UNet Inference")
            self.log("  3: UNet Evaluation")
            self.log("  4: SAM2 Training")
            self.log("  5: SAM2 Inference")
            self.log("  6: SAM2 Evaluation")
            sys.exit(1)

    def clean_previous_checkpoints(self):
        """Clean checkpoints from previous training runs"""
        if not self.clean_checkpoints:
            return

        self.log("🧹 Cleaning previous checkpoints...")

        # Clean UNet checkpoints
        unet_ckpt_dir = self.project_root / "output" / "fs_unet_ckpt"
        if unet_ckpt_dir.exists():
            self.log(f"Removing UNet checkpoints: {unet_ckpt_dir}")
            shutil.rmtree(unet_ckpt_dir)

        # Clean SAM2 checkpoints
        sam2_ckpt_dir = self.project_root / "output" / "finetune_sam_ckpt"
        if sam2_ckpt_dir.exists():
            self.log(f"Removing SAM2 checkpoints: {sam2_ckpt_dir}")
            shutil.rmtree(sam2_ckpt_dir)

        self.log("✅ Previous checkpoints cleaned")

    def run_command(self, cmd: str, description: str) -> bool:
        """Run command with error handling"""
        self.log(f"Starting: {description}")
        self.log(f"Command: {cmd}")

        # Set environment variables
        env = os.environ.copy()
        env["CUDA_VISIBLE_DEVICES"] = self.cuda_devices

        try:
            subprocess.run(
                cmd,
                shell=True,
                cwd=self.project_root,
                env=env,
                check=True,
                capture_output=False,
            )
            self.log(f"✅ SUCCESS: {description}")
            return True
        except subprocess.CalledProcessError:
            self.log(f"❌ FAILED: {description}")
            self.log(f"Command failed: {cmd}")
            response = input("Do you want to continue? (y/N): ")
            if response.lower() != "y":
                self.log("Stopping execution")
                sys.exit(1)
            return False

    def get_latest_unet_checkpoint(self, round_num: int) -> Optional[str]:
        """Get the latest UNet checkpoint path for a specific round"""
        checkpoint_dir = (
            self.project_root / "output" / "fs_unet_ckpt" / f"mitoem_{round_num}"
        )

        # Check for specific checkpoint files
        for checkpoint_name in ["last_checkpoint.pytorch", "best_checkpoint.pytorch"]:
            checkpoint_path = checkpoint_dir / checkpoint_name
            if checkpoint_path.exists():
                return str(checkpoint_path)

        # Find the latest checkpoint file
        if checkpoint_dir.exists():
            checkpoint_files = list(checkpoint_dir.glob("*.pytorch"))
            if checkpoint_files:
                latest_checkpoint = max(
                    checkpoint_files, key=lambda x: x.stat().st_mtime
                )
                return str(latest_checkpoint)

        return None

    def get_sam2_experiment_for_round(self, round_num: int) -> Optional[str]:
        """Get the SAM2 experiment directory for a specific round"""
        sam2_exp_dir = (
            self.project_root / "output" / "finetune_sam_ckpt" / f"mitoem_{round_num}"
        )
        checkpoint_file = sam2_exp_dir / "checkpoints" / "checkpoint.pt"

        if sam2_exp_dir.exists() and checkpoint_file.exists():
            return str(sam2_exp_dir)
        return None

    def should_skip_step(self, current_round: int, current_step: int) -> bool:
        """Check if step should be skipped"""
        if current_round < self.start_round:
            return True
        elif current_round == self.start_round and current_step < self.start_step:
            return True
        return False

    def restore_previous_state(self, target_round: int):
        """Restore state from previous rounds"""
        self.log(f"🔄 Restoring state for round {target_round}...")

        if target_round > 1:
            prev_round = target_round - 1
            self.prev_unet_path = self.get_latest_unet_checkpoint(prev_round)
            if not self.prev_unet_path:
                self.log(
                    f"❌ ERROR: Cannot find UNet checkpoint for previous round {prev_round}"
                )
                sys.exit(1)
            self.log(f"📁 Restored previous UNet path: {self.prev_unet_path}")

            self.prev_sam2_exp = self.get_sam2_experiment_for_round(prev_round)
            if not self.prev_sam2_exp:
                self.log(
                    f"❌ ERROR: Cannot find SAM2 experiment for previous round {prev_round}"
                )
                sys.exit(1)
            self.log(f"📁 Restored previous SAM2 experiment: {self.prev_sam2_exp}")

    def run_pipeline(self):
        """Run the complete training pipeline"""
        self.log("🚀 Starting full training pipeline")
        self.log("Configuration:")
        self.log(f"  - CUDA devices: {self.cuda_devices}")
        self.log(f"  - Number of rounds: {self.num_rounds}")
        self.log(
            f"  - Starting from round: {self.start_round}, step: {self.start_step}"
        )
        self.log(f"  - Project root: {self.project_root}")
        if self.log_file:
            self.log(f"  - Log file: {self.log_file}")

        # Clean previous checkpoints if requested
        self.clean_previous_checkpoints()

        # Restore state if starting from a later round or step
        if self.start_round > 1 or self.start_step > 1:
            self.log("🔄 Restoring state from previous execution...")

            # Restore state for all completed rounds
            for restore_round in range(1, self.start_round):
                self.restore_previous_state(restore_round)

            # If starting from middle of a round, restore that round's state too
            if self.start_step > 1:
                self.restore_previous_state(self.start_round)

        # Main training loop
        for round_num in range(1, self.num_rounds + 1):
            self.log("")
            self.log(f"🔄 ===== ROUND {round_num}/{self.num_rounds} =====")

            # Step 1: UNet Training
            self._run_unet_training(round_num)

            # Get current round UNet checkpoint path
            current_unet_path = self.get_latest_unet_checkpoint(round_num)
            if not current_unet_path:
                self.log(
                    f"❌ ERROR: UNet checkpoint not found after training round {round_num}"
                )
                sys.exit(1)
            self.log(f"📁 UNet checkpoint for round {round_num}: {current_unet_path}")

            # Step 2: UNet Inference
            self._run_unet_inference(round_num, current_unet_path)

            # Step 3: UNet Evaluation
            self._run_unet_evaluation(round_num)

            # Step 4: SAM2 Training
            self._run_sam2_training(round_num)

            # Get current round SAM2 experiment path
            current_sam2_exp = self.get_sam2_experiment_for_round(round_num)
            if not current_sam2_exp:
                self.log(
                    f"❌ ERROR: SAM2 experiment not found after training round {round_num}"
                )
                sys.exit(1)
            self.log(f"📁 SAM2 experiment for round {round_num}: {current_sam2_exp}")

            # Step 5: SAM2 Inference
            self._run_sam2_inference(round_num, current_sam2_exp, current_unet_path)

            # Step 6: SAM2 Evaluation
            self._run_sam2_evaluation(round_num)

            # Update previous paths for next round
            self.prev_unet_path = current_unet_path
            self.prev_sam2_exp = current_sam2_exp

            self.log(f"✅ Round {round_num} completed successfully")

        # Final summary
        self.log("")
        self.log(f"🎉 All {self.num_rounds} rounds completed successfully!")
        self.log(f"Finished at: {datetime.now()}")
        self.log("")
        self.log("📋 Training Summary:")
        self.log(f"  - Total rounds: {self.num_rounds}")
        self.log(f"  - Final UNet checkpoint: {self.prev_unet_path}")
        self.log(f"  - Final SAM2 experiment: {self.prev_sam2_exp}")
        self.log("  - Results can be found in the output directory")

    def _run_unet_training(self, round_num: int):
        """Run UNet training for a specific round"""
        self.log("")
        self.log(f"📚 Step 1: UNet Training (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_TRAIN):
            self.log(f"⏭️  Skipping UNet Training (Round {round_num})")
            return

        # Use absolute path for checkpoint directory
        checkpoint_dir = (
            self.project_root / "output" / "fs_unet_ckpt" / f"mitoem_{round_num}"
        )

        if round_num == 1:
            # First round: use trainu_fs_mitoem config
            unet_config = "trainu_fs_mitoem"
            unet_cmd = f'python train/train_unet.py +task={unet_config} ++task.override_unet_config.trainer.checkpoint_dir="{checkpoint_dir}"'
        else:
            # Subsequent rounds: use trainu_fs_mitoem2 config with previous checkpoint
            unet_config = "trainu_fs_mitoem2"
            if not self.prev_unet_path:
                self.log(
                    f"❌ ERROR: Previous UNet checkpoint not found for round {round_num}"
                )
                sys.exit(1)
            unet_cmd = f'python train/train_unet.py +task={unet_config} ++task.override_unet_config.trainer.checkpoint_dir="{checkpoint_dir}" ++task.override_unet_config.trainer.pre_trained="{self.prev_unet_path}"'

        self.run_command(unet_cmd, f"UNet Training Round {round_num}")

    def _run_unet_inference(self, round_num: int, unet_path: str):
        """Run UNet inference for a specific round"""
        self.log("")
        self.log(f"🔍 Step 2: UNet Inference (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_INFERENCE):
            self.log(f"⏭️  Skipping UNet Inference (Round {round_num})")
            return

        unet_inference_cmd = f'python predict/predict_new.py +task=in_mitoem_u ++task.override_unet_config.model_path="{unet_path}"'
        self.run_command(unet_inference_cmd, f"UNet Inference Round {round_num}")

    def _run_unet_evaluation(self, round_num: int):
        """Run UNet evaluation for a specific round"""
        self.log("")
        self.log(f"📊 Step 3: UNet Evaluation (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_UNET_EVAL):
            self.log(f"⏭️  Skipping UNet Evaluation (Round {round_num})")
            return

        unet_eval_cmd = f'python predict/evaluate.py --config-name evaluate_mitoem_u ++custom_suffix="{round_num}_unet"'
        self.run_command(unet_eval_cmd, f"UNet Evaluation Round {round_num}")

    def _run_sam2_training(self, round_num: int):
        """Run SAM2 training for a specific round"""
        self.log("")
        self.log(f"🎯 Step 4: SAM2 Training (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_TRAIN):
            self.log(f"⏭️  Skipping SAM2 Training (Round {round_num})")
            return

        # Use absolute path for SAM2 experiment directory
        sam2_exp_dir = (
            self.project_root / "output" / "finetune_sam_ckpt" / f"mitoem_{round_num}"
        )

        if round_num == 1:
            # First round: use default config
            sam2_cmd = f'python train/train.py +task=train_sam_mitoem ++task.config_overrides.launcher.experiment_log_dir="{sam2_exp_dir}"'
        else:
            # Subsequent rounds: use previous SAM2 checkpoint
            if not self.prev_sam2_exp:
                self.log(
                    f"❌ ERROR: Previous SAM2 experiment not found for round {round_num}"
                )
                sys.exit(1)
            sam2_checkpoint_path = (
                Path(self.prev_sam2_exp) / "checkpoints" / "checkpoint.pt"
            )
            sam2_cmd = f'python train/train.py +task=train_sam_mitoem2 ++task.config_overrides.launcher.ckpt_path="{sam2_checkpoint_path}" ++task.config_overrides.launcher.experiment_log_dir="{sam2_exp_dir}"'

        self.run_command(sam2_cmd, f"SAM2 Training Round {round_num}")

    def _run_sam2_inference(self, round_num: int, sam2_exp: str, unet_path: str):
        """Run SAM2 inference for a specific round"""
        self.log("")
        self.log(f"🔍 Step 5: SAM2 Inference (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_INFERENCE):
            self.log(f"⏭️  Skipping SAM2 Inference (Round {round_num})")
            return

        sam2_model_path = Path(sam2_exp) / "checkpoints" / "checkpoint.pt"
        sam2_inference_cmd = f'python predict/predict_new.py +task=in_mitoem ++task.sam2_model_path="{sam2_model_path}" ++task.override_unet_config.model_path="{unet_path}"'
        self.run_command(sam2_inference_cmd, f"SAM2 Inference Round {round_num}")

    def _run_sam2_evaluation(self, round_num: int):
        """Run SAM2 evaluation for a specific round"""
        self.log("")
        self.log(f"📊 Step 6: SAM2 Evaluation (Round {round_num})")

        if self.should_skip_step(round_num, self.STEP_SAM2_EVAL):
            self.log(f"⏭️  Skipping SAM2 Evaluation (Round {round_num})")
            return

        sam2_eval_cmd = f'python predict/evaluate.py --config-name evaluate_mitoem ++custom_suffix="{round_num}_sam"'
        self.run_command(sam2_eval_cmd, f"SAM2 Evaluation Round {round_num}")


def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Full Training Pipeline Automation Script (Python Version)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3
  python run_full_training_pipeline.py --cuda-devices 0,1 --num-rounds 3 --start-round 2 --start-step 4
  python run_full_training_pipeline.py --clean-checkpoints --log-dir ./logs
        """,
    )

    parser.add_argument(
        "--cuda-devices",
        default="0",
        help="CUDA devices to use (e.g., '0,1'). Default: '0'",
    )
    parser.add_argument(
        "--num-rounds",
        type=int,
        default=1,
        help="Number of training rounds. Default: 1",
    )
    parser.add_argument(
        "--start-round", type=int, default=1, help="Starting round number. Default: 1"
    )
    parser.add_argument(
        "--start-step",
        type=int,
        default=1,
        help="Starting step number (1-6). Default: 1",
    )
    parser.add_argument("--log-dir", help="Directory to save logs (optional)")
    parser.add_argument(
        "--clean-checkpoints",
        action="store_true",
        help="Clean previous checkpoints before starting",
    )

    args = parser.parse_args()

    # Create and run pipeline
    pipeline = TrainingPipeline(
        cuda_devices=args.cuda_devices,
        num_rounds=args.num_rounds,
        start_round=args.start_round,
        start_step=args.start_step,
        log_dir=args.log_dir,
        clean_checkpoints=args.clean_checkpoints,
    )

    pipeline.run_pipeline()


if __name__ == "__main__":
    main()
