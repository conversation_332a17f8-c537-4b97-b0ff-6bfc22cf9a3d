"""
Example usage of VolumeDataset with preloading capability.

This example demonstrates how to use the enhanced VolumeDataset with
asynchronous preloading for improved training performance.
"""

import time
from SAM2.training.dataset.vos_raw_dataset import VolumeDataset


class MockVolumeIO:
    """Mock volume loader for demonstration purposes."""

    def load(self, path):
        # Simulate loading time
        time.sleep(0.1)  # Simulate I/O delay
        print(f"Loading from {path}")
        return f"data_from_{path}"


def example_training_loop():
    """
    Example training loop showing how to use VolumeDataset with preloading.
    """

    # Mock dataset configuration
    class MockConfig:
        def __init__(self):
            self.normalization_file = "mock_norm.json"
            self.datasets_info = []
            self.root_dir = "/mock/path"

        def get(self, key, default=None):
            return default

    # Create dataset with preloading enabled
    dataset_config = MockConfig()
    volume_io = MockVolumeIO()

    # Initialize dataset with preloading enabled (default)
    dataset = VolumeDataset(
        dataset_config=dataset_config,
        volume_io=volume_io,
        enable_preloading=True,  # Enable async preloading
        resolution=512,
    )

    print("=== Training Loop with Preloading ===")

    # Simulate training epochs
    for epoch in range(3):
        print(f"\n--- Epoch {epoch} ---")

        # Simulate the pattern from set_epoch function
        curr_volume = (
            epoch % len(dataset.datasets) if hasattr(dataset, "datasets") else epoch % 3
        )

        print(f"Requesting dataset {curr_volume}")
        start_time = time.time()

        # Get video data (this will use preloaded data if available)
        video_data = dataset.get_video(curr_volume)

        load_time = time.time() - start_time
        print(f"Dataset {curr_volume} loaded in {load_time:.3f} seconds")

        # Simulate training on this dataset
        print(f"Training on dataset {curr_volume}...")
        time.sleep(0.5)  # Simulate training time

    # Clean up resources
    dataset.cleanup()
    print("\n=== Training Complete ===")


def example_without_preloading():
    """
    Example showing performance without preloading for comparison.
    """

    class MockConfig:
        def __init__(self):
            self.normalization_file = "mock_norm.json"
            self.datasets_info = []
            self.root_dir = "/mock/path"

        def get(self, key, default=None):
            return default

    dataset_config = MockConfig()
    volume_io = MockVolumeIO()

    # Initialize dataset with preloading disabled
    dataset = VolumeDataset(
        dataset_config=dataset_config,
        volume_io=volume_io,
        enable_preloading=False,  # Disable preloading
        resolution=512,
    )

    print("=== Training Loop without Preloading ===")

    # Simulate training epochs
    for epoch in range(3):
        print(f"\n--- Epoch {epoch} ---")

        curr_volume = (
            epoch % len(dataset.datasets) if hasattr(dataset, "datasets") else epoch % 3
        )

        print(f"Requesting dataset {curr_volume}")
        start_time = time.time()

        # Get video data (this will always load synchronously)
        video_data = dataset.get_video(curr_volume)

        load_time = time.time() - start_time
        print(f"Dataset {curr_volume} loaded in {load_time:.3f} seconds")

        # Simulate training on this dataset
        print(f"Training on dataset {curr_volume}...")
        time.sleep(0.5)  # Simulate training time

    print("\n=== Training Complete ===")


def performance_comparison():
    """
    Compare performance with and without preloading.
    """
    print("Performance Comparison:")
    print("=" * 50)

    print("\n1. With Preloading:")
    start_time = time.time()
    example_training_loop()
    with_preload_time = time.time() - start_time

    print("\n2. Without Preloading:")
    start_time = time.time()
    example_without_preloading()
    without_preload_time = time.time() - start_time

    print(f"\n=== Performance Results ===")
    print(f"With preloading:    {with_preload_time:.3f} seconds")
    print(f"Without preloading: {without_preload_time:.3f} seconds")
    print(f"Speedup:           {without_preload_time/with_preload_time:.2f}x")


if __name__ == "__main__":
    # Run the performance comparison
    performance_comparison()
